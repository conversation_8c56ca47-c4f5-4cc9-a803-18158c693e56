# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError
from datetime import timedelta


class ThApplicantSendMail(models.TransientModel):
    _name = 'th.applicant.send.mail'
    _inherit = 'mail.composer.mixin'
    _description = 'Gửi email hàng loạt cho ứng viên theo giai đoạn'

    # Các trường cơ bản
    th_applicant_ids = fields.Many2many(
        'hr.applicant',
        string='Danh sách ứng viên',
        required=True,
        help="Danh sách ứng viên sẽ nhận email"
    )

    th_author_id = fields.Many2one(
        'res.partner',
        string='Người gửi',
        required=True,
        default=lambda self: self.env.user.partner_id.id
    )

    # Thống kê và thông tin
    th_applicant_count = fields.Integer(
        string='Tổng số ứng viên',
        compute='_compute_th_stage_groups',
        help="Tổng số ứng viên đ<PERSON> ch<PERSON>"
    )

    th_applicant_with_email_count = fields.Integer(
        string='Ứng viên có email',
        compute='_compute_th_stage_groups',
        help="Số ứng viên có địa chỉ email hợp lệ"
    )

    th_stage_group_info = fields.Html(
        string='Thông tin nhóm giai đoạn',
        compute='_compute_th_stage_groups',
        help="Chi tiết về các nhóm ứng viên theo giai đoạn"
    )

    @api.depends('subject')
    def _compute_render_model(self):
        """Thiết lập model để render email"""
        self.render_model = 'hr.applicant'

    @api.depends('th_applicant_ids')
    def _compute_th_stage_groups(self):
        """Tính toán thông tin nhóm ứng viên theo giai đoạn"""
        for record in self:
            if not record.th_applicant_ids:
                record.th_applicant_count = 0
                record.th_applicant_with_email_count = 0
                record.th_stage_group_info = ''
                continue

            # Tính tổng số ứng viên
            record.th_applicant_count = len(record.th_applicant_ids)

            # Đếm ứng viên có email hợp lệ
            th_applicants_with_email = record.th_applicant_ids.filtered(
                lambda a: record.th_get_applicant_email(a)
            )
            record.th_applicant_with_email_count = len(th_applicants_with_email)

            # Nhóm ứng viên theo stage_id
            th_stage_groups = {}
            for applicant in record.th_applicant_ids:
                stage = applicant.stage_id
                if stage not in th_stage_groups:
                    th_stage_groups[stage] = []
                th_stage_groups[stage].append(applicant)

            # Đếm ứng viên có template
            th_applicants_with_template = 0
            th_info_html = '<div class="th_stage_groups_info d-flex flex-column">'

            for stage, applicants in th_stage_groups.items():
                has_template = bool(stage.template_id)
                if has_template:
                    th_applicants_with_template += len(applicants)

                # Đếm ứng viên có email trong nhóm này
                th_applicants_with_email_in_group = [
                    a for a in applicants
                    if self.th_get_applicant_email(a)
                ]

                # Tạo thông tin HTML cho nhóm
                th_status_class = 'text-success' if has_template else 'text-warning'
                th_template_name = stage.template_id.name if has_template else 'Chưa cấu hình'

                th_info_html += f'''
                <div class="mb-2 p-2 border rounded">
                    <strong>{stage.name}</strong>
                    <span class="badge badge-primary">{len(applicants)} ứng viên</span>
                    <span class="badge badge-info">{len(th_applicants_with_email_in_group)} có email</span>
                    <br/>
                    <small class="{th_status_class}">
                        <i class="fa fa-envelope"></i> Template: {th_template_name}
                    </small>
                </div>
                '''

            th_info_html += '</div>'
            record.th_stage_group_info = th_info_html

    def th_get_stage_groups(self):
        """Lấy danh sách nhóm ứng viên theo giai đoạn"""
        th_stage_groups = {}
        for applicant in self.th_applicant_ids:
            stage = applicant.stage_id
            if stage not in th_stage_groups:
                th_stage_groups[stage] = {
                    'stage': stage,
                    'applicants': [],
                    'template': stage.template_id,
                    'has_template': bool(stage.template_id)
                }
            th_stage_groups[stage]['applicants'].append(applicant)
        return list(th_stage_groups.values())

    def th_validate_applicants(self):
        """Kiểm tra tính hợp lệ của danh sách ứng viên"""
        if not self.th_applicant_ids:
            raise UserError(_("Vui lòng chọn ít nhất một ứng viên để gửi email."))

        # Kiểm tra có ứng viên nào có template không
        th_stage_groups = self.th_get_stage_groups()
        th_groups_with_template = [g for g in th_stage_groups if g['has_template']]

        if not th_groups_with_template:
            raise UserError(_(
                "Không có ứng viên nào ở giai đoạn có cấu hình template email. "
                "Vui lòng cấu hình template cho các giai đoạn tuyển dụng trước."
            ))

        # Kiểm tra ứng viên có email
        th_applicants_without_email = []
        for group in th_groups_with_template:
            for applicant in group['applicants']:
                th_email = self.th_get_applicant_email(applicant)
                if not th_email:
                    th_applicants_without_email.append(applicant)

        if th_applicants_without_email:
            th_names = ', '.join([a.partner_name or a.name for a in th_applicants_without_email])
            raise UserError(_(
                "Các ứng viên sau không có địa chỉ email: %s. "
                "Vui lòng cập nhật email trước khi gửi."
            ) % th_names)

    def th_get_applicant_email(self, th_applicant):
        """Lấy địa chỉ email của ứng viên"""
        if th_applicant.email_from:
            return th_applicant.email_from
        else:
            return False
    def th_create_partner_if_needed(self, th_applicant):
        """Tạo partner cho ứng viên nếu chưa có"""
        if not th_applicant.partner_id:
            th_applicant.partner_id = self.env['res.partner'].create({
                'is_company': False,
                'type': 'private',
                'name': th_applicant.partner_name or th_applicant.name,
                'email': th_applicant.email_from,
                'phone': th_applicant.partner_phone,
                'mobile': th_applicant.partner_mobile,
            })
        elif th_applicant.partner_id and not th_applicant.partner_id.email and th_applicant.email_from:
            # Cập nhật email cho partner nếu chưa có
            th_applicant.partner_id.email = th_applicant.email_from

    def th_calculator_dealine_date(self, datetime):
        """Tính toán ngày deadline cho ứng viên"""
        if datetime:
            return (datetime - timedelta(days=1)).strftime('%d/%m/%Y')
        else:
            return ''
    def th_get_address(self,area):
        if area == 'hn':
            return '116 Trần Vỹ, Mai Dịch, Cầu Giấy, Hà Nội '
        else:
            return '91 Ký Con, Nguyễn Thái Bình, Quận 1, TP Hồ Chí Minh'

    def th_send_direct_email(self, th_template, th_applicant):
        """Gửi email trực tiếp qua SMTP tới ứng viên"""
        try:
            # Lấy địa chỉ email đích
            th_recipient_email = self.th_get_applicant_email(th_applicant)
            if not th_recipient_email:
                raise ValueError(f"Không tìm thấy email cho ứng viên {th_applicant.partner_name or th_applicant.name}")

            # Đảm bảo ứng viên có partner để render template đúng
            self.th_create_partner_if_needed(th_applicant)

            # xử lý ngày phỏng vấn thành dạng '%H:%M ngày %d/%m/%Y'
            th_interview_date_str = th_applicant.th_interview_call_date.strftime('%d/%m/%Y') if th_applicant.th_interview_call_date else ''
            th_interview_time_str = th_applicant.th_interview_call_date.strftime('%H:%M') if th_applicant.th_interview_call_date else ''
            th_interview_datetime_str = f"{th_interview_time_str} ngày {th_interview_date_str}" if th_interview_date_str and th_interview_time_str else ''
            
            # xử lý địa chỉ phỏng vấn
            th_interview_address = self.th_get_address(th_applicant.th_interview_area)
            
            # xử lý dealine thời gian phỏng vấn (th_interview_datetime_str - 1 day)
            th_interview_deadline_str = self.th_calculator_dealine_date(th_applicant.th_interview_call_date) if th_applicant.th_interview_call_date else ''
            
            # Tạo context để render template - tương tự như th_send_mail_duplicate
            ctx = {
                'email_from': self.th_author_id.email if self.th_author_id.email else self.env.user.email,
                'email_to': th_recipient_email,
                'applicant_name': th_applicant.partner_name or th_applicant.name,
                'job_name': th_applicant.job_id.name if th_applicant.job_id else '',
                'stage_name': th_applicant.stage_id.name if th_applicant.stage_id else '',
                'company_name': th_applicant.company_id.name if th_applicant.company_id else '',
                'partner_name': th_applicant.partner_name if th_applicant.partner_id else '',
                'company_address': th_applicant.company_id.street if th_applicant.company_id else '',
                'partner_phone': th_applicant.partner_phone if th_applicant else '',
                'partner_email': th_applicant.email_from if th_applicant else '',
                'th_interview_call_date': th_applicant.th_interview_call_date.strftime('%d/%m/%Y') if th_applicant.th_interview_call_date else '',
                'th_interview_datetime_str': th_interview_datetime_str, 
                'th_interview_address' : th_interview_address,
                'th_interview_deadline_str': th_interview_deadline_str,
                'th_training_address': self.th_get_address(th_applicant.th_training_area),
                'th_training_time': th_applicant.th_training_time if th_applicant.th_training_time else '',
                'th_training_start_date': th_applicant.th_training_start_date.strftime('%d/%m/%Y') if th_applicant.th_training_start_date else '',
                'th_training_end_date': th_applicant.th_training_end_date.strftime('%d/%m/%Y') if th_applicant.th_training_end_date else '',
                'th_training_mail_confirmation_time': th_applicant.th_training_email_confirmation_time.strftime('%d/%m/%Y') if th_applicant.th_training_email_confirmation_time else '',
                'th_trial_reception_date': th_applicant.th_trial_reception_date.strftime('%d/%m/%Y') if th_applicant.th_trial_reception_date else '',
                'th_trial_mail_confirmation_time': th_applicant.th_trial_email_confirmation_time.strftime('%d/%m/%Y') if th_applicant.th_trial_email_confirmation_time else '',
            }

            # Sử dụng with_context để render template với dữ liệu cụ thể
            th_template_ctx = th_template.with_context(
                lang=th_applicant.partner_id.lang if th_applicant.partner_id else 'vi_VN',
                **ctx
            )

            # Render email trực tiếp thông qua send_mail với force_send=False để lấy nội dung
            th_mail_id = th_template_ctx.send_mail(
                th_applicant.id, 
                force_send=False,
                email_values={
                    'email_to': th_recipient_email,
                    'email_from': ctx['email_from'],
                }
            )

            # Lấy mail record vừa tạo và gửi
            th_mail = self.env['mail.mail'].browse(th_mail_id)
            if th_mail:
                th_mail.send()

                # Ghi log vào chatter của ứng viên để theo dõi
                th_applicant.message_post(
                    subject=f"Đã gửi email: {th_template_ctx.name}",
                    body=f"Email đã được gửi tới {th_recipient_email} sử dụng template '{th_template.name}'",
                    message_type='comment',
                    subtype_xmlid='mail.mt_note'
                )

                return True
            else:
                raise ValueError("Không thể tạo mail record")

        except Exception as e:
            # Ghi log lỗi vào chatter
            th_applicant.message_post(
                subject="Lỗi gửi email",
                body=f"Không thể gửi email tới {th_recipient_email}: {str(e)}",
                message_type='comment',
                subtype_xmlid='mail.mt_note'
            )
            raise e

    def th_send_emails_by_stage(self):
        """Gửi email theo giai đoạn với template tương ứng"""
        th_stage_groups = self.th_get_stage_groups()
        th_groups_with_template = [g for g in th_stage_groups if g['has_template']]

        th_total_sent = 0
        th_failed_applicants = []
        th_stage_results = []

        for group in th_groups_with_template:
            stage = group['stage']
            template = group['template']
            applicants = group['applicants']

            # Lọc ứng viên có email
            th_valid_applicants = [
                a for a in applicants
                if self.th_get_applicant_email(a)
            ]

            th_sent_in_group = 0
            th_failed_in_group = []

            for applicant in th_valid_applicants:
                try:
                    # Gửi email trực tiếp qua SMTP
                    self.th_send_direct_email(template, applicant)

                    th_sent_in_group += 1
                    th_total_sent += 1

                except Exception as e:
                    th_failed_in_group.append(applicant.partner_name or applicant.name)
                    th_failed_applicants.append(applicant.partner_name or applicant.name)

            # Lưu kết quả cho từng stage
            th_stage_results.append({
                'stage_name': stage.name,
                'template_name': template.name,
                'sent_count': th_sent_in_group,
                'failed_count': len(th_failed_in_group),
                'failed_names': th_failed_in_group
            })

        # Tạo thông báo kết quả chi tiết
        return self.th_create_result_notification(th_total_sent, th_failed_applicants, th_stage_results)

    def th_create_result_notification(self, th_total_sent, th_failed_applicants, th_stage_results):
        """Tạo thông báo kết quả gửi email"""
        if not th_failed_applicants:
            # Thành công hoàn toàn
            th_message_parts = [f"Đã gửi email thành công cho {th_total_sent} ứng viên"]

            # Chi tiết theo stage
            for result in th_stage_results:
                th_message_parts.append(
                    f"• {result['stage_name']}: {result['sent_count']} email ({result['template_name']})"
                )

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'type': 'success',
                    'message':'\n'.join(th_message_parts),
                    'sticky': True,
                }
            }
        else:
            # Có lỗi
            th_message_parts = [
                f"Đã gửi email cho {th_total_sent} ứng viên",
                f"Không thể gửi cho {len(th_failed_applicants)} ứng viên: {', '.join(th_failed_applicants)}"
            ]

            # Chi tiết theo stage
            for result in th_stage_results:
                if result['sent_count'] > 0:
                    th_message_parts.append(
                        f"• {result['stage_name']}: {result['sent_count']} thành công"
                    )
                if result['failed_count'] > 0:
                    th_message_parts.append(
                        f"• {result['stage_name']}: {result['failed_count']} thất bại"
                    )

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'type': 'warning',
                    'message': '\n'.join(th_message_parts),
                    'sticky': True,
                }
            }

    def action_send_email(self):
        """Action chính để gửi email hoàng loạt theo giai đoạn"""
        self.ensure_one()

        # Validate dữ liệu đầu vào
        self.th_validate_applicants()

        # Hiển thị confirmation dialog nếu gửi cho nhiều ứng viên
        if len(self.th_applicant_ids) > 5:
            return {
                'type': 'ir.actions.act_window',
                'name': _('Xác nhận gửi email'),
                'res_model': 'th.applicant.send.mail.confirm',
                'view_mode': 'form',
                'target': 'new',
                'context': {
                    'default_wizard_id': self.id,
                    'default_applicant_count': len(self.th_applicant_ids),
                },
                'reload': True,
            }

        # Gửi email theo giai đoạn
        return self.th_send_emails_by_stage()

    def th_force_send_email(self):
        """Gửi email mà không cần confirmation"""
        return self.th_send_emails_by_stage()

class ThApplicantSendMailConfirm(models.TransientModel):
    _name = 'th.applicant.send.mail.confirm'
    _description = 'Xác nhận gửi email hàng loạt'

    wizard_id = fields.Many2one('th.applicant.send.mail', string='Wizard', required=True)
    applicant_count = fields.Integer(string='Số lượng ứng viên', required=True)

    def action_confirm_send(self):
        """Xác nhận và gửi email"""
        self.ensure_one()
        return self.wizard_id.th_force_send_email()

    def action_cancel(self):
        """Hủy gửi email"""
        return {'type': 'ir.actions.act_window_close'}
