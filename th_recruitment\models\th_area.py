from odoo import models, fields, api
from odoo.exceptions import ValidationError


class ThArea(models.Model):
    _name = 'th.area'
    _description = 'Cấu hình khu vực'

    name = fields.Char(string='Tên khu vực')
    th_area_code = fields.Char(string='Mã khu vực')

    # tự sinh mã khu vực khi tạo mới
    @api.model
    def default_get(self, fields_list):
        defaults = super(ThArea, self).default_get(fields_list)
        if 'th_area_code' in fields_list:
            # Tìm mã khu vực lớn nhất hiện tại
            areas = self.search([('th_area_code', 'like', 'kv%')])
            max_code = 0
            for area in areas:
                try:
                    code_num = int(area.th_area_code[2:])
                    if code_num > max_code:
                        max_code = code_num
                except:
                    continue
            # Tạo mã mới
            new_code = max_code + 1
            defaults['th_area_code'] = f'kv{new_code:02d}'
        return defaults

    # kiểm tra tên khu vực có trùng lặp không
    @api.constrains('name', 'th_area_code')
    def _th_check_unique_area(self):
        for record in self:
            name_count = self.search_count([
                ('name', '=', record.name),
                ('id', '!=', record.id)
            ])
            if name_count > 0:
                raise ValidationError('Tên khu vực đã tồn tại!')

    # Không được xóa dữ liệu mẫu
    def unlink(self):
        model_data = self.env['ir.model.data'].search([
            ('model', '=', self._name),
            ('res_id', 'in', self.ids)
        ])
        if model_data:
            raise ValidationError("Không được xóa dữ liệu mẫu!")
        return super().unlink()


