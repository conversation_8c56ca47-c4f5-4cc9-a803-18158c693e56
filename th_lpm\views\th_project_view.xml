<odoo>
    <record id="th_project_view_kanban" model="ir.ui.view">
        <field name="name">th_project_view_kanban</field>
        <field name="model">th.project.lpm</field>
        <field name="arch" type="xml">
            <kanban  default_group_by="th_catalog_warehouse_Selection" action="action_th_product_manufacturing" type="object">
                <field name="name"/>
                <field name="th_description"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_global_click">
                            <div t-attf-class="o_kanban_card_header">
                                <div class="o_kanban_card_header_title">
                                    <div class="o_kanban_record_title oe_kanban_details">
                                        <strong>
                                            <h4>
                                                <field name="name"/>
                                            </h4>
                                        </strong>
                                    </div>
                                    <div t-if="record.th_description">
                                        <i class="fa fa-info-circle" title="Description" role="img"
                                           aria-label="Description"></i>Description
                                        <t t-esc="record.th_description.value"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
      <record id="th_project_view_tree" model="ir.ui.view">
        <field name="name">th.project.view.tree</field>
        <field name="model">th.project.lpm</field>
        <field name="arch" type="xml">
            <tree string="Dự án" editable="bottom" create="false">
                <field name="name" readonly="1" widget="tree_url" optional="show"/>
                <field name="th_university_id" readonly="1" optional="show"/>
                <field name="th_production_number" optional="show"/>
                <field name="th_total_production_costs" optional="show"/>
                <field name="th_total_object_costs" optional="show"/>
                <field name="th_catalog_warehouse_Selection" optional="show"/>
                <field name="th_total_proposed_costs" optional="show"/>
            </tree>
        </field>
    </record>
    <record id="th_project_view_form" model="ir.ui.view">
        <field name="name">th_project_view_form</field>
        <field name="model">th.project.lpm</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <field name="readonly_domain" invisible="1"/>
                    <group>
                        <group>
                            <field name="th_project_code" required="1"/>
                            <field name="name"/>
                            <field name="th_production_number"/>
                            <field name="th_implementation_unit"/>
                            <field name="th_lead_domain" invisible="1"/>
                            <field name="th_members_domain" invisible="1"/>
                            <field name="th_team_id" string="Đội nhóm" options="{'no_create': True,'no_edit': True, 'no_open':True}"/>
                            <field name="th_lead_the_project" options="{'no_create': True,'no_edit': True, 'no_open':True}" domain="th_lead_domain"/>
                            <field name="th_project_members" widget="many2many_tags" options="{'no_create': True,'no_edit': True, 'no_open':True}" domain="th_members_domain"/>
                            <field name="th_selection_costs" />
                            <field name="th_percent_cost_qa" />
                            <field name="th_cost_qa" widget="monetary" readonly="1"/>
                            <field name="th_percent_costs_incurred" />
                            <field name="th_costs_incurred" readonly="1"/>
<!--                            <field name="th_catalog_warehouse_id" options="{'no_open': 1}"/>-->
                        </group>
                        <group name='group_right'>
                        <field name="th_university_id" options="{'no_open': 1}"/>
                        <field name="th_start_date"/>
                        <field name="th_end_date" />
                        <field name="th_catalog_warehouse_Selection" options="{'no_open': 1}"/>
                        <field name="th_total_production_costs" widget="monetary"/>
                        <field name="th_total_object_costs" widget="monetary"/>
                        <field name="th_total_proposed_costs" widget="monetary"/>

                        </group>
                    </group>
                    <notebook>
                        <page string="Mô tả">
                            <field name="th_description"/>

                        </page>
                        <page string="Phạm vi dự án">
                            <field name="th_project_scope"/>
                        </page>
                    </notebook>
                </sheet>

                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>
     <record id="action_view_search_project" model="ir.ui.view">
        <field name="name">action_view_search_project</field>
        <field name="model">th.project.lpm</field>
        <field name="arch" type="xml">
            <search string="State">
                <group expand="0" string="Group By">
                    <filter string="Danh mục sản xuất" name="th_catalog_warehouse_Selection" context="{'group_by': 'th_catalog_warehouse_Selection'}"/>
                </group>
            </search>
        </field>
    </record>
    <record id="th_project_view_kanban_action" model="ir.actions.act_window">
        <field name="name">Dự án</field> 
        <field name="res_model">th.project.lpm</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="domain">[('th_university_id', '=', active_id)]</field>
        <field name="search_view_id" ref="action_view_search_project"/>
        <field name="context">{'create': True,'search_default_th_catalog_warehouse_id': True,'default_th_university_id': active_id}</field>
        <field name="view_id" ref="th_project_view_kanban"/>

    </record>
    <record id="action_over_view_search_project" model="ir.ui.view">
        <field name="name">action_over_view_search_project</field>
        <field name="model">th.project.lpm</field>
        <field name="arch" type="xml">
            <search string="State">
                <group expand="0" string="Group By">
                    <filter string="Trường" name="th_university_id" context="{'group_by': 'th_university_id'}"/>
                </group>
            </search>
        </field>
    </record>
    <record id="th_project_over_view_action" model="ir.actions.act_window">
        <field name="name">Dự án</field>
        <field name="res_model">th.project.lpm</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="search_view_id" ref="action_over_view_search_project"/>
        <field name="context">{'search_default_th_catalog_warehouse_id': True,}</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'kanban', 'view_id': ref('th_project_view_kanban')}),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('th_project_view_tree')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('th_project_view_form')})]"/>
    </record>
    <record id="th_project_over_tree_view_action" model="ir.actions.act_window">
        <field name="name">Kế hoạch</field>
        <field name="res_model">th.project.lpm</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="search_view_id" ref="action_over_view_search_project"/>
        <field name="context">{'search_default_th_university_id': True,}</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('th_project_view_tree')}),
            (0, 0, {'view_mode': 'kanban', 'view_id': ref('th_project_view_kanban')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('th_project_view_form')})]"/>
    </record>
</odoo>