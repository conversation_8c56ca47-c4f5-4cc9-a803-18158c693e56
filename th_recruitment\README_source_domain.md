# Cấu hình Domain cho Trường Source_ID trong HR Applicant

## <PERSON><PERSON> tả
Tính năng này giới hạn các lựa chọn trong trường `source_id` (Nguồn) của form ứng viên (`hr.applicant`) chỉ hiển thị những nguồn đã được cấu hình trong `th.channel.conf`.

## Cách hoạt động

### 1. Model th.channel.conf
- Chứa danh sách các nguồn được phép sử dụng
- Mỗi nguồn có:
  - `name`: <PERSON><PERSON><PERSON> nguồn (ví dụ: Facebook, LinkedIn, Twitter)
  - `th_channel_code`: <PERSON><PERSON> nguồn (tự động sinh)

### 2. Logic Domain
- Trường `th_source_domain` trong `hr.applicant` được tính toán tự động
- Domain được tạo dựa trên danh sách tên nguồn từ `th.channel.conf`
- <PERSON><PERSON><PERSON> c<PERSON> cấu hình: `[('name', 'in', [danh_sách_tên_nguồn])]`
- <PERSON><PERSON><PERSON> không có cấu hình: `[('id', '=', False)]` (không hiển thị nguồn nào)

### 3. Áp dụng trong View
- Trường `source_id` trong form `hr_applicant_view_form` sử dụng domain từ `th_source_domain`
- Chỉ hiển thị các `utm.source` có tên trùng với cấu hình trong `th.channel.conf`

## Cách sử dụng

### 1. Cấu hình nguồn
1. Vào menu cấu hình `th.channel.conf`
2. Tạo các nguồn mới với tên tương ứng với `utm.source`
3. Ví dụ: Tạo nguồn với tên "Facebook", "LinkedIn", "Twitter"

### 2. Tạo UTM Source
1. Vào menu UTM Sources
2. Tạo các nguồn với tên trùng với cấu hình trong `th.channel.conf`
3. Ví dụ: Tạo utm.source với name="Facebook", name="LinkedIn", name="Twitter"

### 3. Sử dụng trong form ứng viên
1. Mở form ứng viên (`hr.applicant`)
2. Trường "Source" sẽ chỉ hiển thị các nguồn đã cấu hình
3. Không thể chọn nguồn nào khác ngoài danh sách đã cấu hình

## Ví dụ

### Cấu hình th.channel.conf:
```
- Facebook
- LinkedIn  
- Twitter
```

### UTM Sources có sẵn:
```
- Facebook    ✅ (sẽ hiển thị)
- LinkedIn    ✅ (sẽ hiển thị)
- Twitter     ✅ (sẽ hiển thị)
- Google      ❌ (sẽ bị ẩn)
- Instagram   ❌ (sẽ bị ẩn)
```

### Kết quả:
Trong form ứng viên, dropdown "Source" chỉ hiển thị: Facebook, LinkedIn, Twitter

## Files thay đổi

1. **th_recruitment/models/th_hr_applicant.py**
   - Thêm trường `th_source_domain`
   - Thêm method `_compute_source_domain()`

2. **th_recruitment/views/th_hr_applicant_view.xml**
   - Thêm trường ẩn `th_source_domain`
   - Áp dụng domain cho trường `source_id`

3. **th_recruitment/tests/test_source_domain.py**
   - Test cases cho logic domain

## Lưu ý
- Domain được tính toán lại mỗi khi form được load
- Nếu xóa hết cấu hình trong `th.channel.conf`, trường source_id sẽ không hiển thị nguồn nào
- Cần đảm bảo tên trong `th.channel.conf` trùng chính xác với tên trong `utm.source`
