from odoo import models, fields, api
from odoo.exceptions import ValidationError


class ThChannelConf(models.Model):
    _name = 'th.channel.conf'
    _description = 'Cấu hình nguồn'

    name = fields.Char(string='Tên nguồn')
    th_channel_code = fields.Char(string='Mã nguồn')

    # tự sinh mã nguồn khi tạo mới
    @api.model
    def default_get(self, fields_list):
        defaults = super(ThChannelConf, self).default_get(fields_list)
        if 'th_channel_code' in fields_list:
            # Tìm mã khu vực lớn nhất hiện tại
            areas = self.search([('th_channel_code', 'like', 'nguon%')])
            max_code = 0
            for area in areas:
                try:
                    code_num = int(area.th_channel_code[5:])
                    if code_num > max_code:
                        max_code = code_num
                except:
                    continue
            # Tạo mã mới
            new_code = max_code + 1
            defaults['th_channel_code'] = f'nguon{new_code:02d}'
        return defaults

    # kiểm tra tên nguồn có trùng lặp không
    @api.constrains('name')
    def _th_check_unique_channel(self):
        for record in self:
            name_count = self.search_count([
                ('name', '=', record.name),
                ('id', '!=', record.id)
            ])
            if name_count > 0:
                raise ValidationError('Tên nguồn đã tồn tại!')

    # Không được xóa dữ liệu mẫu
    def unlink(self):
        model_data = self.env['ir.model.data'].search([
            ('model', '=', self._name),
            ('res_id', 'in', self.ids)
        ])
        if model_data:
            raise ValidationError("Không được xóa dữ liệu mẫu!")
        return super().unlink()


