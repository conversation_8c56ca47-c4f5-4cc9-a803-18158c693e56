from odoo import models, fields, api, _


class ThHrApplicant(models.Model):
    _inherit = 'hr.applicant'

    th_recruitment_campaign_id = fields.Many2one('th.recruitment.campaign', string='Đợt chiêu binh')
    th_interview_call_date = fields.Datetime(string='Ngày gọi mời phỏng vấn')
    th_interview_appointment_date = fields.Datetime(string='Ngày hẹn phỏng vấn')
    th_cv_status = fields.Selection([('agreed', 'Đồng ý phỏng vấn'),
                                     ('refused', 'Từ chối phỏng vấn'),
                                     ('department_refused', 'BP từ chối'),
                                     ('not_answer_phone', '<PERSON>hông nghe máy'),
                                     ('unreachable_phone', 'Thu<PERSON> bao'),
                                     ('processing', '<PERSON>ang xử lý'),
                                     ('not_finalized', 'UV chưa chốt lịch'),
                                     ('duplicated', 'Trùng'),
                                     ('error_number', 'Sai số'),
                                     ('storaged', '<PERSON><PERSON><PERSON> kho')],
                                    string='Tình trạng CV')
    th_letter_status = fields.Selection([('not_sent_yet', '<PERSON>ưa gửi'),
                                         ('sent', 'Đã gửi'),
                                         ('no_response', '<PERSON>hông phản hồi'),
                                         ('canceled', 'Đã hủy')],
                                        string='Thư mời phỏng vấn')
    th_interview_time = fields.Float(string='Giờ phỏng vấn')
    th_interview_area = fields.Selection([('hn', 'VP HN'),
                                          ('hcm', 'VP HCM')],
                                         string='Khu vực phỏng vấn')
    th_interview_format = fields.Selection([('offline', 'Offline'),
                                            ('online', 'Online')],
                                           string='Hình thức phỏng vấn')
    th_interview_status = fields.Selection([('appointment', 'Hẹn PV'),
                                            ('participate', 'Tham gia'),
                                            ('not_participate', 'Không tham gia'),
                                            ('refused', 'Từ chối'),
                                            ('reschedule', 'Chuyển lịch PV')],
                                           string='Tình trạng phỏng vấn')
    th_interview_results = fields.Selection([('passed', 'Đạt'),
                                             ('failed', 'Không đạt'),
                                             ('department_considered', 'BP cân nhắc')],
                                            string='Kết quả phỏng vấn')
    th_email_confirmation_time = fields.Datetime(string='Thời gian xác nhận mail của ứng viên')
    th_training_start_date = fields.Date(string='Ngày bắt đầu hội nhập')
    th_training_end_date = fields.Date(string='Ngày kết thúc hội nhập')
    th_training_time = fields.Float(string='Giờ training, hội nhập')
    th_training_format = fields.Selection([('offline', 'Offline'),
                                           ('online', 'Online')],
                                          string='Hình thức training, hội nhập')
    th_training_area = fields.Selection([('hn', 'VP HN'),
                                         ('hcm', 'VP HCM')],
                                        string='Khu vực training')
    th_training_letter_status = fields.Selection([('not_sent_yet', 'Chưa gửi'),
                                                  ('sent', 'Đã gửi'),
                                                  ('no_response', 'Không phản hồi'),
                                                  ('canceled', 'Đã hủy')],
                                                 string='Thư mời training')
    th_training_status = fields.Selection([('participate', 'Tham gia'),
                                           ('not_participate', 'Không tham gia'),
                                           ('refused', 'Từ chối')],
                                          string='Tình trạng training')
    th_training_results = fields.Selection([('passed', 'Đạt training'),
                                            ('failed', 'Không đạt training'),
                                            ('stopped', 'Dừng training')],
                                           string='Kết quả sau training')
    th_training_email_confirmation_time = fields.Datetime(string='Thời gian xác nhận mail training')
    th_trial_reception_date = fields.Date(string='Ngày tiếp nhận thử việc')
    th_trial_letter_status = fields.Selection([('not_sent_yet', 'Chưa gửi'),
                                               ('sent', 'Đã gửi'),
                                               ('no_response', 'Không phản hồi'),
                                               ('canceled', 'Đã hủy')],
                                              string='Thư mời thử việc')
    th_trial_status = fields.Selection([('accepted', 'Tiếp nhận thử việc'),
                                        ('not_accepted', 'Không tiếp nhận thử việc'),
                                        ('stopped', 'Dừng thử việc')],
                                       string='Tình trạng thử việc')
    th_trial_email_confirmation_time = fields.Datetime(string='Thời gian xác nhận mail thử việc')
    th_date_of_birth = fields.Date(string='Ngày sinh')

    # Computed field để tạo domain cho source_id
    th_source_domain = fields.Char(compute='_compute_source_domain', string='Domain nguồn')

    @api.depends()
    def _compute_source_domain(self):
        """Tính toán domain cho trường source_id dựa trên cấu hình trong th.channel.conf"""
        # Lấy danh sách tên nguồn từ th.channel.conf
        channel_names = self.env['th.channel.conf'].search([]).mapped('name')

        # Tạo domain để chỉ hiển thị các utm.source có tên trong danh sách
        if channel_names:
            domain = [('name', 'in', channel_names)]
        else:
            # Nếu không có cấu hình nào, không hiển thị nguồn nào
            domain = [('id', '=', False)]

        for record in self:
            record.th_source_domain = str(domain)

    def th_action_send_email_with_template(self):
        """Mở wizard gửi email với template cho ứng viên hiện tại"""
        self.ensure_one()
        return {
            'name': _('Gửi email với template'),
            'type': 'ir.actions.act_window',
            'res_model': 'th.applicant.send.mail',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_th_applicant_ids': self.ids,
                'default_th_use_template': True,
            }
        }

    def th_action_send_bulk_email(self):
        """Mở wizard gửi email hàng loạt cho nhiều ứng viên"""
        return {
            'name': _('Gửi email hàng loạt'),
            'type': 'ir.actions.act_window',
            'res_model': 'th.applicant.send.mail',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_th_applicant_ids': self.ids,
                'default_th_use_template': True,
            }
        }
