from odoo import models, fields, api
from odoo.exceptions import ValidationError


class ThRecruitmentCampaignConf(models.Model):
    _name = 'th.recruitment.campaign'
    _description = '<PERSON><PERSON>u hình đợt chiêu binh'

    name = fields.Char(string='Tên đợt')
    th_recruitment_campaign_code = fields.Char(string='Mã đợt')
    th_start_date = fields.Date(string='Ngày bắt đầu')
    th_end_date = fields.Date(string='Ngày kết thúc')
    th_area_id = fields.Many2one('th.area', string='Khu vực')
    th_recruitment_ids = fields.One2many('hr.applicant', 'th_recruitment_campaign_id', string='Danh sách chiêu binh')

    # tự sinh mã đợt khi tạo mới
    @api.model
    def default_get(self, fields_list):
        defaults = super(ThRecruitmentCampaignConf, self).default_get(fields_list)
        if 'th_recruitment_campaign_code' in fields_list:
            # Tìm mã đợt lớn nhất hiện tại
            areas = self.search([('th_recruitment_campaign_code', 'like', 'dot%')])
            max_code = 0
            for area in areas:
                try:
                    code_num = int(area.th_recruitment_campaign_code[3:])
                    if code_num > max_code:
                        max_code = code_num
                except:
                    continue
            # Tạo mã mới
            new_code = max_code + 1
            defaults['th_recruitment_campaign_code'] = f'dot{new_code:02d}'
        return defaults

    # kiểm tra tên đợt có trùng lặp không
    @api.constrains('name')
    def _th_check_unique_campaign(self):
        for record in self:
            name_count = self.search_count([
                ('name', '=', record.name),
                ('id', '!=', record.id)
            ])
            if name_count > 0:
                raise ValidationError('Tên đợt chiêu binh đã tồn tại!')

    # Kiểm tra ngày bắt đầu không được lớn hơn ngày kết thúc
    @api.constrains('th_start_date', 'th_end_date')
    def _th_check_start_end_dates(self):
        for rec in self:
            if rec.th_start_date and rec.th_end_date:
                if rec.th_start_date > rec.th_end_date:
                    raise ValidationError("Ngày bắt đầu không được lớn hơn ngày kết thúc.")
