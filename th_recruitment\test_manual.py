#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script để test thủ công logic domain cho source_id
"""

def test_domain_logic():
    """Test logic tạo domain"""
    
    # <PERSON><PERSON><PERSON> lập dữ liệu từ th.channel.conf
    channel_names_case1 = ['Facebook', 'LinkedIn', 'Twitter']
    channel_names_case2 = []  # Trường hợp không có cấu hình
    
    # Test case 1: <PERSON><PERSON> cấu hình
    if channel_names_case1:
        domain1 = [('name', 'in', channel_names_case1)]
    else:
        domain1 = [('id', '=', False)]
    
    print("Test case 1 - Có cấu hình:")
    print(f"Channel names: {channel_names_case1}")
    print(f"Domain: {domain1}")
    print(f"Domain string: {str(domain1)}")
    print()
    
    # Test case 2: Không có cấu hình
    if channel_names_case2:
        domain2 = [('name', 'in', channel_names_case2)]
    else:
        domain2 = [('id', '=', False)]
    
    print("Test case 2 - Không có cấu hình:")
    print(f"Channel names: {channel_names_case2}")
    print(f"Domain: {domain2}")
    print(f"Domain string: {str(domain2)}")
    print()
    
    # Test case 3: Thêm nguồn mới
    channel_names_case3 = channel_names_case1 + ['Google']
    if channel_names_case3:
        domain3 = [('name', 'in', channel_names_case3)]
    else:
        domain3 = [('id', '=', False)]
    
    print("Test case 3 - Thêm nguồn mới:")
    print(f"Channel names: {channel_names_case3}")
    print(f"Domain: {domain3}")
    print(f"Domain string: {str(domain3)}")
    
    print("\n✅ Logic test completed successfully!")

if __name__ == '__main__':
    test_domain_logic()
