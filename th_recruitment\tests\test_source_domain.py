# -*- coding: utf-8 -*-
from odoo.tests.common import TransactionCase


class TestSourceDomain(TransactionCase):
    
    def setUp(self):
        super(TestSourceDomain, self).setUp()
        
        # Tạo dữ liệu test cho th.channel.conf
        self.channel_conf_1 = self.env['th.channel.conf'].create({
            'name': 'Facebook',
        })
        self.channel_conf_2 = self.env['th.channel.conf'].create({
            'name': 'LinkedIn',
        })
        
        # Tạo dữ liệu test cho utm.source
        self.utm_source_1 = self.env['utm.source'].create({
            'name': 'Facebook',
        })
        self.utm_source_2 = self.env['utm.source'].create({
            'name': 'LinkedIn',
        })
        self.utm_source_3 = self.env['utm.source'].create({
            'name': 'Google',  # <PERSON>hông có trong th.channel.conf
        })
        
        # Tạo job để test
        self.job = self.env['hr.job'].create({
            'name': 'Test Job',
        })
    
    def test_source_domain_computation(self):
        """Test tính toán domain cho source_id"""
        # Tạo applicant
        applicant = self.env['hr.applicant'].create({
            'name': 'Test Applicant',
            'job_id': self.job.id,
        })
        
        # Kiểm tra domain được tính toán đúng
        expected_domain = str([('name', 'in', ['Facebook', 'LinkedIn'])])
        self.assertEqual(applicant.th_source_domain, expected_domain)
    
    def test_source_domain_empty_config(self):
        """Test domain khi không có cấu hình nào trong th.channel.conf"""
        # Xóa tất cả cấu hình
        self.env['th.channel.conf'].search([]).unlink()
        
        # Tạo applicant
        applicant = self.env['hr.applicant'].create({
            'name': 'Test Applicant',
            'job_id': self.job.id,
        })
        
        # Kiểm tra domain trả về empty
        expected_domain = str([('id', '=', False)])
        self.assertEqual(applicant.th_source_domain, expected_domain)
    
    def test_source_domain_updates_when_config_changes(self):
        """Test domain cập nhật khi cấu hình thay đổi"""
        # Tạo applicant
        applicant = self.env['hr.applicant'].create({
            'name': 'Test Applicant',
            'job_id': self.job.id,
        })
        
        # Domain ban đầu
        initial_domain = applicant.th_source_domain
        
        # Thêm cấu hình mới
        self.env['th.channel.conf'].create({
            'name': 'Twitter',
        })
        
        # Trigger recompute
        applicant._compute_source_domain()
        
        # Domain phải thay đổi
        new_domain = applicant.th_source_domain
        self.assertNotEqual(initial_domain, new_domain)
        self.assertIn('Twitter', new_domain)
