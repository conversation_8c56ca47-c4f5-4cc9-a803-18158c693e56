<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Server Action để gửi email hàng loạt từ list view ứng viên -->
        <record id="th_action_send_bulk_email" model="ir.actions.server">
            <field name="name">Gửi email hàng loạt</field>
            <field name="model_id" ref="hr_recruitment.model_hr_applicant"/>
            <field name="binding_model_id" ref="hr_recruitment.model_hr_applicant"/>
            <field name="binding_view_types">list</field>
            <field name="state">code</field>
            <field name="code">
# Ki<PERSON><PERSON> tra có ứng viên được chọn không
if not records:
    raise UserError("Vui lòng chọn ít nhất một ứng viên để gửi email.")

# Mở wizard gửi email hàng loạt
action = {
    'name': 'Gửi email hàng loạt cho ứng viên',
    'type': 'ir.actions.act_window',
    'res_model': 'th.applicant.send.mail',
    'view_mode': 'form',
    'target': 'new',
    'context': {
        'default_th_applicant_ids': records.ids,
        'default_th_use_template': True,
    }
}
            </field>
        </record>

        <!-- Server Action để gửi email từ form view ứng viên -->
        <record id="th_action_send_email_single" model="ir.actions.server">
            <field name="name">Gửi email với template</field>
            <field name="model_id" ref="hr_recruitment.model_hr_applicant"/>
            <field name="binding_model_id" ref="hr_recruitment.model_hr_applicant"/>
            <field name="binding_view_types">form</field>
            <field name="state">code</field>
            <field name="code">
# Mở wizard gửi email cho ứng viên hiện tại
action = {
    'name': 'Gửi email cho ứng viên',
    'type': 'ir.actions.act_window',
    'res_model': 'th.applicant.send.mail',
    'view_mode': 'form',
    'target': 'new',
    'context': {
        'default_th_applicant_ids': [record.id],
        'default_th_use_template': True,
    }
}
            </field>
        </record>

    </data>
</odoo>
